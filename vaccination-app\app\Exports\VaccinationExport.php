namespace App\Exports;

use App\Models\Vaccination;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Support\Facades\DB;

class VaccinationExport implements FromCollection
{
    private $quarter;
    private $year;

    public function __construct($quarter, $year)
    {
        $this->quarter = $quarter;
        $this->year = $year;
    }

    public function collection()
    {
        $months = $this->getMonthsForQuarter();
        $vaccinations = Vaccination::where('year', $this->year)
            ->whereIn('month', $months)
            ->select('vaccine_name', DB::raw('SUM(quantity) as total_quantity'))
            ->groupBy('vaccine_name')
            ->get();

        return $vaccinations;
    }

    private function getMonthsForQuarter()
    {
        switch ($this->quarter) {
            case 1:
                return ['janvier', 'février', 'mars'];
            case 2:
                return ['avril', 'mai', 'juin'];
            case 3:
                return ['juillet', 'août', 'septembre'];
            case 4:
                return ['octobre', 'novembre', 'décembre'];
            default:
                throw new \Exception('Trimestre invalide');
        }
    }
}