namespace App\Exports;

use App\Models\Vaccination;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Illuminate\Support\Facades\DB;

class VaccinationExport implements
    FromCollection,
    WithHeadings,
    WithMapping,
    WithTitle,
    ShouldAutoSize
{
    private $quarter;
    private $year;

    public function __construct($quarter, $year)
    {
        $this->quarter = $quarter;
        $this->year = $year;
    }

    public function collection()
    {
        $months = Vaccination::getMonthsForQuarter($this->quarter);

        return Vaccination::byQuarter($this->quarter, $this->year)
            ->select('vaccine_name', DB::raw('SUM(quantity) as total_quantity'))
            ->groupBy('vaccine_name')
            ->orderBy('vaccine_name')
            ->get();
    }

    public function headings(): array
    {
        return [
            'Nom du Vaccin',
            'Quantité Totale',
            'Trimestre',
            'Année'
        ];
    }

    public function map($vaccination): array
    {
        return [
            $vaccination->vaccine_name,
            $vaccination->total_quantity,
            'Q' . $this->quarter,
            $this->year
        ];
    }

    public function title(): string
    {
        return "Vaccinations Q{$this->quarter} {$this->year}";
    }
}