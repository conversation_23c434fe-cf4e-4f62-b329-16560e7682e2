<!DOCTYPE html>
<html>
<head>
    <title>Tableau de bord des vaccinations</title>
    <style>
        table { border-collapse: collapse; }
        th, td { border: 1px solid black; padding: 8px; }
    </style>
</head>
<body>
    <h1>Vaccinations pour {{ $selectedYear }}</h1>
    <form method="GET">
        <select name="year" onchange="this.form.submit()">
            @foreach ($years as $y)
                <option value="{{ $y }}" {{ $y == $selectedYear ? 'selected' : '' }}>{{ $y }}</option>
            @endforeach
        </select>
    </form>

    <table>
        <thead>
            <tr>
                <th>Mois</th>
                @foreach ($vaccines as $vaccine)
                    <th>{{ $vaccine }}</th>
                @endforeach
            </tr>
        </thead>
        <tbody>
            @foreach ($months as $month)
                <tr>
                    <td>{{ $month }}</td>
                    @foreach ($vaccines as $vaccine)
                        <td>{{ $data[$month][$vaccine] ?? 0 }}</td>
                    @endforeach
                </tr>
            @endforeach
        </tbody>
    </table>

    <h2>Exporter par trimestre</h2>
    @for ($q = 1; $q <= 4; $q++)
        <a href="{{ route('export', ['quarter' => $q, 'year' => $selectedYear]) }}">Exporter Q{{ $q }}</a>
    @endfor
</body>
</html>