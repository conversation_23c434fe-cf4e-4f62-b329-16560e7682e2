<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord des vaccinations</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .export-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            margin-top: 2rem;
        }
        .btn-export {
            margin: 0.25rem;
        }
        .loading {
            display: none;
        }
    </style>
</head>
<body class="bg-light">
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0"><i class="fas fa-syringe me-3"></i>Tableau de bord des vaccinations</h1>
                    <p class="mb-0 mt-2">Données pour l'année {{ $selectedYear }}</p>
                </div>
                <div class="col-md-4">
                    <form method="GET" class="d-flex">
                        <select name="year" class="form-select" onchange="showLoading(); this.form.submit()">
                            @foreach ($years as $y)
                                <option value="{{ $y }}" {{ $y == $selectedYear ? 'selected' : '' }}>{{ $y }}</option>
                            @endforeach
                        </select>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="loading text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>

        @if($vaccines->count() > 0)
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <h3 class="text-primary">{{ $vaccines->count() }}</h3>
                        <p class="mb-0">Types de vaccins</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <h3 class="text-success">{{ array_sum(array_map('array_sum', $data)) }}</h3>
                        <p class="mb-0">Total vaccinations</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <h3 class="text-info">{{ count(array_filter($data, function($month) { return array_sum($month) > 0; })) }}</h3>
                        <p class="mb-0">Mois actifs</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <h3 class="text-warning">{{ round(array_sum(array_map('array_sum', $data)) / 12) }}</h3>
                        <p class="mb-0">Moyenne mensuelle</p>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th><i class="fas fa-calendar-alt me-2"></i>Mois</th>
                            @foreach ($vaccines as $vaccine)
                                <th class="text-center">{{ $vaccine }}</th>
                            @endforeach
                            <th class="text-center"><strong>Total</strong></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($months as $month)
                            <tr>
                                <td class="fw-bold">{{ ucfirst($month) }}</td>
                                @php $monthTotal = 0; @endphp
                                @foreach ($vaccines as $vaccine)
                                    @php
                                        $quantity = $data[$month][$vaccine] ?? 0;
                                        $monthTotal += $quantity;
                                    @endphp
                                    <td class="text-center">
                                        @if($quantity > 0)
                                            <span class="badge bg-primary">{{ number_format($quantity) }}</span>
                                        @else
                                            <span class="text-muted">0</span>
                                        @endif
                                    </td>
                                @endforeach
                                <td class="text-center fw-bold">
                                    <span class="badge bg-success">{{ number_format($monthTotal) }}</span>
                                </td>
                            </tr>
                        @endforeach
                        <!-- Totals Row -->
                        <tr class="table-secondary fw-bold">
                            <td>Total</td>
                            @foreach ($vaccines as $vaccine)
                                @php $vaccineTotal = array_sum(array_column($data, $vaccine)); @endphp
                                <td class="text-center">{{ number_format($vaccineTotal) }}</td>
                            @endforeach
                            <td class="text-center">{{ number_format(array_sum(array_map('array_sum', $data))) }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Export Section -->
            <div class="export-section">
                <h3 class="mb-3"><i class="fas fa-download me-2"></i>Exporter par trimestre</h3>
                <div class="row">
                    @for ($q = 1; $q <= 4; $q++)
                        <div class="col-md-3">
                            <a href="{{ route('export', ['quarter' => $q, 'year' => $selectedYear]) }}"
                               class="btn btn-outline-primary btn-export w-100">
                                <i class="fas fa-file-excel me-2"></i>Trimestre {{ $q }}
                            </a>
                        </div>
                    @endfor
                </div>
                <div class="mt-3">
                    <a href="{{ route('import.form') }}" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>Importer de nouvelles données
                    </a>
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-chart-bar fa-5x text-muted mb-3"></i>
                <h3>Aucune donnée disponible</h3>
                <p class="text-muted">Aucune donnée de vaccination trouvée pour l'année {{ $selectedYear }}.</p>
                <a href="{{ route('import.form') }}" class="btn btn-primary">
                    <i class="fas fa-upload me-2"></i>Importer des données
                </a>
            </div>
        @endif
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showLoading() {
            document.querySelector('.loading').style.display = 'block';
        }
    </script>
</body>
</html>