namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\VaccinationImport;

class ImportController extends Controller
{
    public function showImportForm()
    {
        return view('import');
    }

    public function import(Request $request)
    {
        $file = $request->file('file');
        $filename = $file->getClientOriginalName();
        $parts = explode('_', pathinfo($filename, PATHINFO_FILENAME));
        $month = $parts[0];
        $year = $parts[1];

        Excel::import(new VaccinationImport($month, $year), $file);

        return redirect()->back()->with('success', 'Données importées avec succès');
    }
}