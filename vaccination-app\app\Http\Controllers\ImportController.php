namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\VaccinationImport;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;

class ImportController extends Controller
{
    private const ALLOWED_MONTHS = [
        'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
        'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
    ];

    public function showImportForm()
    {
        return view('import');
    }

    public function import(Request $request)
    {
        // Validate the request
        $request->validate([
            'file' => [
                'required',
                'file',
                'mimes:xlsx,xls,csv',
                'max:10240', // 10MB max
            ]
        ]);

        try {
            $file = $request->file('file');
            $filename = $file->getClientOriginalName();

            // Parse filename to extract month and year
            $fileInfo = $this->parseFilename($filename);

            // Import the data
            Excel::import(new VaccinationImport($fileInfo['month'], $fileInfo['year']), $file);

            // Clear dashboard cache since new data was imported
            $this->clearDashboardCache($fileInfo['year']);

            Log::info('Vaccination data imported successfully', [
                'filename' => $filename,
                'month' => $fileInfo['month'],
                'year' => $fileInfo['year']
            ]);

            return redirect()->back()->with('success', 'Données importées avec succès');

        } catch (ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            Log::error('Import failed', [
                'error' => $e->getMessage(),
                'filename' => $filename ?? 'unknown'
            ]);

            return redirect()->back()
                ->with('error', 'Erreur lors de l\'importation: ' . $e->getMessage())
                ->withInput();
        }
    }

    private function parseFilename($filename)
    {
        $parts = explode('_', pathinfo($filename, PATHINFO_FILENAME));

        if (count($parts) < 2) {
            throw new \InvalidArgumentException('Le nom du fichier doit être au format: mois_année.xlsx');
        }

        $month = strtolower(trim($parts[0]));
        $year = trim($parts[1]);

        // Validate month
        if (!in_array($month, self::ALLOWED_MONTHS)) {
            throw new \InvalidArgumentException('Mois invalide. Utilisez: ' . implode(', ', self::ALLOWED_MONTHS));
        }

        // Validate year
        if (!is_numeric($year) || $year < 1900 || $year > 2100) {
            throw new \InvalidArgumentException('Année invalide. Doit être entre 1900 et 2100.');
        }

        return ['month' => $month, 'year' => (int)$year];
    }

    private function clearDashboardCache($year)
    {
        Cache::forget("dashboard_data_{$year}");
    }
}