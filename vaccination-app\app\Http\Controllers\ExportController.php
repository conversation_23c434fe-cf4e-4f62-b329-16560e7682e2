namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\VaccinationExport;

class ExportController extends Controller
{
    public function export(Request $request, $quarter)
    {
        $year = $request->input('year', 2023);
        return Excel::download(new VaccinationExport($quarter, $year), "vaccinations_Q{$quarter}_{$year}.xlsx");
    }
}