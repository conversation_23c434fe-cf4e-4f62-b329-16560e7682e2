<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\Vaccination;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class VaccinationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('local');
    }

    public function test_dashboard_displays_vaccination_data()
    {
        // Create test data
        Vaccination::create([
            'month' => 'janvier',
            'year' => 2023,
            'vaccine_name' => 'COVID-19',
            'quantity' => 100
        ]);

        Vaccination::create([
            'month' => 'février',
            'year' => 2023,
            'vaccine_name' => 'Grippe',
            'quantity' => 50
        ]);

        $response = $this->get('/dashboard?year=2023');

        $response->assertStatus(200);
        $response->assertSee('Données pour l\'année 2023');
        $response->assertSee('COVID-19');
        $response->assertSee('Grippe');
    }

    public function test_dashboard_shows_empty_state_when_no_data()
    {
        $response = $this->get('/dashboard?year=2023');

        $response->assertStatus(200);
        $response->assertSee('Aucune donnée disponible');
    }

    public function test_dashboard_validates_year_parameter()
    {
        $response = $this->get('/dashboard?year=invalid');
        $response->assertStatus(200);
        
        // Should default to current year when invalid year provided
        $currentYear = date('Y');
        $response->assertSee("Données pour l'année {$currentYear}");
    }

    public function test_import_form_displays_correctly()
    {
        $response = $this->get('/import');

        $response->assertStatus(200);
        $response->assertSee('Importer des données');
        $response->assertSee('Format requis');
    }

    public function test_import_validates_file_requirements()
    {
        // Test missing file
        $response = $this->post('/import', []);
        $response->assertSessionHasErrors(['file']);

        // Test invalid file type
        $file = UploadedFile::fake()->create('test.txt', 100);
        $response = $this->post('/import', ['file' => $file]);
        $response->assertSessionHasErrors(['file']);
    }

    public function test_export_generates_excel_file()
    {
        // Create test data for Q1
        Vaccination::create([
            'month' => 'janvier',
            'year' => 2023,
            'vaccine_name' => 'COVID-19',
            'quantity' => 100
        ]);

        $response = $this->get('/export/1?year=2023');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }

    public function test_vaccination_model_scopes()
    {
        // Create test data
        Vaccination::create(['month' => 'janvier', 'year' => 2023, 'vaccine_name' => 'COVID-19', 'quantity' => 100]);
        Vaccination::create(['month' => 'février', 'year' => 2023, 'vaccine_name' => 'Grippe', 'quantity' => 50]);
        Vaccination::create(['month' => 'janvier', 'year' => 2022, 'vaccine_name' => 'COVID-19', 'quantity' => 75]);

        // Test byYear scope
        $vaccinations2023 = Vaccination::byYear(2023)->get();
        $this->assertCount(2, $vaccinations2023);

        // Test byMonth scope
        $vaccinationsJanvier = Vaccination::byMonth('janvier')->get();
        $this->assertCount(2, $vaccinationsJanvier);

        // Test byVaccine scope
        $covidVaccinations = Vaccination::byVaccine('COVID-19')->get();
        $this->assertCount(2, $covidVaccinations);

        // Test byQuarter scope
        $q1Vaccinations = Vaccination::byQuarter(1, 2023)->get();
        $this->assertCount(2, $q1Vaccinations);
    }

    public function test_vaccination_model_helper_methods()
    {
        // Test getMonthsForQuarter
        $q1Months = Vaccination::getMonthsForQuarter(1);
        $this->assertEquals(['janvier', 'février', 'mars'], $q1Months);

        $q4Months = Vaccination::getMonthsForQuarter(4);
        $this->assertEquals(['octobre', 'novembre', 'décembre'], $q4Months);

        // Test invalid quarter
        $this->expectException(\InvalidArgumentException::class);
        Vaccination::getMonthsForQuarter(5);
    }
}
