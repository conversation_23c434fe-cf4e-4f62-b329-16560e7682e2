{"name": "laravel/vaccination-app", "description": "A Laravel application for managing vaccination data.", "type": "project", "require": {"php": "^7.3|^8.0", "fideloper/proxy": "^4.4", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "laravel/framework": "^8.0", "laravel/tinker": "^2.0", "maatwebsite/excel": "^3.1"}, "require-dev": {"facade/ignition": "^2.5", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "php artisan package:discover --ansi"], "post-update-cmd": ["php artisan vendor:publish --tag=laravel-assets --force"]}, "minimum-stability": "dev", "prefer-stable": true}