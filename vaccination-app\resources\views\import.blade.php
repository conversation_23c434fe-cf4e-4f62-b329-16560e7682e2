<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Importer des données de vaccination</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .import-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .import-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .file-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .file-upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .file-upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .requirements-card {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1.5rem;
            border-radius: 5px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="import-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0"><i class="fas fa-upload me-3"></i>Importer des données</h1>
                    <p class="mb-0 mt-2">Téléchargez vos fichiers Excel de vaccination</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ route('dashboard') }}" class="btn btn-light">
                        <i class="fas fa-chart-bar me-2"></i>Retour au tableau de bord
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Alerts -->
        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if ($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Erreurs de validation :</strong>
                <ul class="mb-0 mt-2">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        <div class="row">
            <div class="col-lg-8">
                <!-- Import Form -->
                <div class="import-card">
                    <h3 class="mb-4"><i class="fas fa-file-excel me-2 text-success"></i>Télécharger un fichier Excel</h3>

                    <form action="{{ route('import') }}" method="POST" enctype="multipart/form-data" id="importForm">
                        @csrf
                        <div class="file-upload-area" id="fileUploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>Glissez-déposez votre fichier ici</h5>
                            <p class="text-muted">ou cliquez pour sélectionner un fichier</p>
                            <input type="file" name="file" id="fileInput" class="d-none" accept=".xlsx,.xls,.csv" required>
                            <div id="fileInfo" class="mt-3" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-file me-2"></i>
                                    <span id="fileName"></span>
                                    <span id="fileSize" class="text-muted"></span>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" disabled>
                                <i class="fas fa-upload me-2"></i>Importer les données
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Requirements -->
                <div class="requirements-card">
                    <h5><i class="fas fa-info-circle me-2"></i>Format requis</h5>
                    <ul class="list-unstyled mt-3">
                        <li><i class="fas fa-check text-success me-2"></i>Format : .xlsx, .xls, .csv</li>
                        <li><i class="fas fa-check text-success me-2"></i>Taille max : 10 MB</li>
                        <li><i class="fas fa-check text-success me-2"></i>Nom : mois_année.xlsx</li>
                    </ul>

                    <h6 class="mt-4">Colonnes requises :</h6>
                    <ul class="list-unstyled">
                        <li><code>vaccine_name</code> - Nom du vaccin</li>
                        <li><code>quantity</code> - Quantité</li>
                    </ul>

                    <h6 class="mt-4">Exemples de noms :</h6>
                    <ul class="list-unstyled text-muted small">
                        <li>janvier_2023.xlsx</li>
                        <li>février_2023.xlsx</li>
                        <li>mars_2023.xlsx</li>
                    </ul>
                </div>

                <!-- Sample Template -->
                <div class="text-center mt-4">
                    <a href="#" class="btn btn-outline-secondary" onclick="downloadTemplate()">
                        <i class="fas fa-download me-2"></i>Télécharger un modèle
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const submitBtn = document.getElementById('submitBtn');

        // Click to select file
        fileUploadArea.addEventListener('click', () => fileInput.click());

        // File selection
        fileInput.addEventListener('change', handleFileSelect);

        // Drag and drop
        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });

        fileUploadArea.addEventListener('dragleave', () => {
            fileUploadArea.classList.remove('dragover');
        });

        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                fileName.textContent = file.name;
                fileSize.textContent = `(${formatFileSize(file.size)})`;
                fileInfo.style.display = 'block';
                submitBtn.disabled = false;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function downloadTemplate() {
            // Create a simple CSV template
            const csvContent = "vaccine_name,quantity\nCOVID-19,100\nGrippe,50\nROR,25";
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'template_vaccination.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        }

        // Form submission loading state
        document.getElementById('importForm').addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Importation en cours...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>