<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVaccinationsTable extends Migration
{
    public function up()
    {
        Schema::create('vaccinations', function (Blueprint $table) {
            $table->id();
            $table->string('month', 20)->index(); // Add index and limit length
            $table->year('year')->index(); // Use year type and add index
            $table->string('vaccine_name', 100)->index(); // Add index and limit length
            $table->unsignedInteger('quantity'); // Use unsigned integer
            $table->timestamps();

            // Add composite indexes for common queries
            $table->index(['year', 'month']);
            $table->index(['year', 'vaccine_name']);
            $table->index(['year', 'month', 'vaccine_name']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('vaccinations');
    }
}