namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Vaccination;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $selectedYear = $request->input('year', 2023);
        $vaccinations = Vaccination::where('year', $selectedYear)->get();
        $months = ['janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];
        $vaccines = Vaccination::where('year', $selectedYear)->distinct()->pluck('vaccine_name');
        $years = Vaccination::distinct()->pluck('year');

        $data = [];
        foreach ($months as $month) {
            $data[$month] = [];
            foreach ($vaccines as $vaccine) {
                $quantity = $vaccinations->where('month', $month)->where('vaccine_name', $vaccine)->sum('quantity');
                $data[$month][$vaccine] = $quantity;
            }
        }

        return view('dashboard', compact('data', 'months', 'vaccines', 'years', 'selectedYear'));
    }
}