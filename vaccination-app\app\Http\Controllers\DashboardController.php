namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Vaccination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class DashboardController extends Controller
{
    private const MONTHS = [
        'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
        'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
    ];

    public function index(Request $request)
    {
        $selectedYear = $request->input('year', date('Y'));

        // Validate year input
        if (!is_numeric($selectedYear) || $selectedYear < 1900 || $selectedYear > 2100) {
            $selectedYear = date('Y');
        }

        // Cache the dashboard data for 10 minutes
        $cacheKey = "dashboard_data_{$selectedYear}";
        $dashboardData = Cache::remember($cacheKey, 600, function () use ($selectedYear) {
            return $this->getDashboardData($selectedYear);
        });

        return view('dashboard', array_merge($dashboardData, [
            'selectedYear' => $selectedYear,
            'months' => self::MONTHS
        ]));
    }

    private function getDashboardData($year)
    {
        // Get all vaccination data for the year in a single optimized query
        $vaccinations = Vaccination::select('month', 'vaccine_name', DB::raw('SUM(quantity) as total_quantity'))
            ->where('year', $year)
            ->groupBy('month', 'vaccine_name')
            ->get()
            ->groupBy(['month', 'vaccine_name']);

        // Get unique vaccines and years
        $vaccines = Vaccination::where('year', $year)
            ->distinct()
            ->orderBy('vaccine_name')
            ->pluck('vaccine_name');

        $years = Vaccination::distinct()
            ->orderBy('year', 'desc')
            ->pluck('year');

        // Build the data matrix efficiently
        $data = [];
        foreach (self::MONTHS as $month) {
            $data[$month] = [];
            foreach ($vaccines as $vaccine) {
                $data[$month][$vaccine] = $vaccinations[$month][$vaccine][0]->total_quantity ?? 0;
            }
        }

        return compact('data', 'vaccines', 'years');
    }
}