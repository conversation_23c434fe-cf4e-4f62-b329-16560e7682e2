<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexesToVaccinationsTable extends Migration
{
    public function up()
    {
        Schema::table('vaccinations', function (Blueprint $table) {
            // Add individual indexes if they don't exist
            if (!$this->indexExists('vaccinations', 'vaccinations_month_index')) {
                $table->index('month');
            }
            if (!$this->indexExists('vaccinations', 'vaccinations_year_index')) {
                $table->index('year');
            }
            if (!$this->indexExists('vaccinations', 'vaccinations_vaccine_name_index')) {
                $table->index('vaccine_name');
            }
            
            // Add composite indexes for common queries
            if (!$this->indexExists('vaccinations', 'vaccinations_year_month_index')) {
                $table->index(['year', 'month']);
            }
            if (!$this->indexExists('vaccinations', 'vaccinations_year_vaccine_name_index')) {
                $table->index(['year', 'vaccine_name']);
            }
            if (!$this->indexExists('vaccinations', 'vaccinations_year_month_vaccine_name_index')) {
                $table->index(['year', 'month', 'vaccine_name']);
            }
        });
    }

    public function down()
    {
        Schema::table('vaccinations', function (Blueprint $table) {
            $table->dropIndex(['year', 'month', 'vaccine_name']);
            $table->dropIndex(['year', 'vaccine_name']);
            $table->dropIndex(['year', 'month']);
            $table->dropIndex(['vaccine_name']);
            $table->dropIndex(['year']);
            $table->dropIndex(['month']);
        });
    }

    private function indexExists($table, $index)
    {
        $connection = Schema::getConnection();
        $doctrineSchemaManager = $connection->getDoctrineSchemaManager();
        $doctrineTable = $doctrineSchemaManager->listTableDetails($table);
        
        return $doctrineTable->hasIndex($index);
    }
}
