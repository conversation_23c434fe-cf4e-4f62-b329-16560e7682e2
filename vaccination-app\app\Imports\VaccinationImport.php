namespace App\Imports;

use App\Models\Vaccination;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class VaccinationImport implements ToModel, WithHeadingRow
{
    private $month;
    private $year;

    public function __construct($month, $year)
    {
        $this->month = $month;
        $this->year = $year;
    }

    public function model(array $row)
    {
        return new Vaccination([
            'month' => $this->month,
            'year' => $this->year,
            'vaccine_name' => $row['vaccine_name'],
            'quantity' => $row['quantity'],
        ]);
    }
}