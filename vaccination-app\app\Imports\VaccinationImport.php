namespace App\Imports;

use App\Models\Vaccination;
use Maat<PERSON>bsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Illuminate\Support\Facades\Log;

class VaccinationImport implements
    ToModel,
    WithHeadingRow,
    WithValidation,
    WithBatchInserts,
    WithChunkReading,
    SkipsOnError
{
    use SkipsErrors;

    private $month;
    private $year;

    public function __construct($month, $year)
    {
        $this->month = $month;
        $this->year = $year;
    }

    public function model(array $row)
    {
        // Skip empty rows
        if (empty($row['vaccine_name']) || empty($row['quantity'])) {
            return null;
        }

        return new Vaccination([
            'month' => $this->month,
            'year' => $this->year,
            'vaccine_name' => trim($row['vaccine_name']),
            'quantity' => (int) $row['quantity'],
        ]);
    }

    public function rules(): array
    {
        return [
            'vaccine_name' => 'required|string|max:100',
            'quantity' => 'required|numeric|min:0',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'vaccine_name.required' => 'Le nom du vaccin est requis',
            'vaccine_name.max' => 'Le nom du vaccin ne peut pas dépasser 100 caractères',
            'quantity.required' => 'La quantité est requise',
            'quantity.numeric' => 'La quantité doit être un nombre',
            'quantity.min' => 'La quantité ne peut pas être négative',
        ];
    }

    public function batchSize(): int
    {
        return 1000; // Process 1000 rows at a time
    }

    public function chunkSize(): int
    {
        return 1000; // Read 1000 rows at a time
    }

    public function onError(\Throwable $error)
    {
        Log::error('Import row error', [
            'error' => $error->getMessage(),
            'month' => $this->month,
            'year' => $this->year
        ]);
    }
}