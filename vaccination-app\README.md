# vaccination-app/README.md

# Vaccination App

This is a Laravel application designed to manage vaccination data. The application allows users to import and export vaccination records using Excel files, as well as view vaccination statistics in a dashboard format.

## Features

- Import vaccination data from Excel files.
- Export vaccination data by quarter in Excel format.
- View vaccination statistics by year and month in a user-friendly dashboard.

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/vaccination-app.git
   ```

2. Navigate to the project directory:
   ```
   cd vaccination-app
   ```

3. Install the dependencies:
   ```
   composer install
   ```

4. Set up the environment file:
   ```
   cp .env.example .env
   ```

5. Configure your database settings in the `.env` file.

6. Generate the application key:
   ```
   php artisan key:generate
   ```

7. Run the migrations:
   ```
   php artisan migrate
   ```

8. Start the development server:
   ```
   php artisan serve
   ```

## Usage

- Navigate to `/import` to upload vaccination data via Excel files.
- Navigate to `/dashboard` to view vaccination statistics.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any improvements or bug fixes.

## License

This project is licensed under the MIT License.