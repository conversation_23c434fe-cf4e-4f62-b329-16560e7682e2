# 💉 Vaccination Management System

A modern, optimized Laravel application for managing vaccination data with advanced import/export capabilities and comprehensive analytics dashboard.

## ✨ Features

### Core Functionality
- **📊 Interactive Dashboard** - Real-time vaccination statistics with beautiful visualizations
- **📤 Excel Import** - Bulk import vaccination data with validation and error handling
- **📥 Excel Export** - Export data by quarters with professional formatting
- **🔍 Advanced Filtering** - Filter data by year, month, vaccine type, and quarters

### Performance & Security
- **⚡ Database Optimization** - Indexed queries and efficient data aggregation
- **🚀 Caching System** - Redis/file-based caching for improved performance
- **🛡️ Security Features** - Rate limiting, input validation, and file upload security
- **📱 Responsive Design** - Mobile-friendly Bootstrap 5 interface

### Developer Experience
- **🧪 Comprehensive Testing** - Unit and feature tests with high coverage
- **📝 Clean Code** - PSR-12 compliant with proper documentation
- **🔧 Error Handling** - Robust error handling and logging
- **🎯 Type Safety** - Proper type hints and validation

## 🚀 Quick Start

### Prerequisites
- PHP 8.0+
- Composer
- MySQL/PostgreSQL
- Node.js & NPM (for frontend assets)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/vaccination-app.git
   cd vaccination-app
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Database configuration**
   ```bash
   # Edit .env file with your database credentials
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=vaccination_app
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

6. **Run migrations**
   ```bash
   php artisan migrate
   ```

7. **Build frontend assets**
   ```bash
   npm run dev
   ```

8. **Start the development server**
   ```bash
   php artisan serve
   ```

Visit `http://localhost:8000` to access the application.

## 📋 Usage Guide

### Dashboard
- Access the main dashboard at `/dashboard` or `/`
- Select different years using the dropdown
- View comprehensive statistics and data tables
- Export quarterly reports directly from the dashboard

### Data Import
1. Navigate to `/import`
2. Prepare your Excel file with the required format:
   - Filename: `month_year.xlsx` (e.g., `janvier_2023.xlsx`)
   - Required columns: `vaccine_name`, `quantity`
3. Upload the file using drag-and-drop or file selection
4. Review import results and error messages

### Data Export
- Click on quarterly export buttons in the dashboard
- Files are generated in Excel format with proper headers
- Includes aggregated data for the selected quarter and year

## 🏗️ Architecture

### Database Schema
```sql
vaccinations
├── id (Primary Key)
├── month (String, Indexed)
├── year (Year, Indexed)
├── vaccine_name (String, Indexed)
├── quantity (Unsigned Integer)
├── created_at
└── updated_at

-- Composite Indexes for Performance
├── INDEX(year, month)
├── INDEX(year, vaccine_name)
└── INDEX(year, month, vaccine_name)
```

### Key Components
- **Models**: `Vaccination` with scopes and validation
- **Controllers**: Optimized with caching and error handling
- **Import/Export**: Laravel Excel with batch processing
- **Views**: Bootstrap 5 with responsive design
- **Tests**: Comprehensive feature and unit tests

## 🔧 Configuration

### Caching
```php
// config/cache.php
'default' => env('CACHE_DRIVER', 'file'),
'stores' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'cache',
    ],
],
```

### Rate Limiting
- Import routes: 10 requests per minute
- Export routes: 30 requests per minute
- Dashboard: No limits (cached responses)

## 🧪 Testing

Run the test suite:
```bash
# Run all tests
php artisan test

# Run with coverage
php artisan test --coverage

# Run specific test file
php artisan test tests/Feature/VaccinationTest.php
```

## 📈 Performance Optimizations

### Database
- ✅ Composite indexes for common query patterns
- ✅ Optimized aggregation queries
- ✅ Efficient data loading with proper relationships

### Application
- ✅ Query result caching (10-minute TTL)
- ✅ Batch processing for imports
- ✅ Lazy loading and pagination
- ✅ Asset optimization and minification

### Frontend
- ✅ CDN-hosted Bootstrap and FontAwesome
- ✅ Optimized images and icons
- ✅ Progressive enhancement
- ✅ Loading states and user feedback

## 🛡️ Security Features

- **File Upload Validation**: Type, size, and content validation
- **Input Sanitization**: All user inputs are validated and sanitized
- **Rate Limiting**: Prevents abuse of import/export endpoints
- **CSRF Protection**: All forms include CSRF tokens
- **Error Handling**: Secure error messages without sensitive data exposure

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow PSR-12 coding standards
- Write tests for new features
- Update documentation as needed
- Use meaningful commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues or have questions:
1. Check the [Issues](https://github.com/yourusername/vaccination-app/issues) page
2. Create a new issue with detailed information
3. Contact the development team

## 🎯 Roadmap

- [ ] API endpoints for mobile applications
- [ ] Advanced analytics and reporting
- [ ] Multi-language support
- [ ] Real-time notifications
- [ ] Data visualization charts
- [ ] Automated backup system