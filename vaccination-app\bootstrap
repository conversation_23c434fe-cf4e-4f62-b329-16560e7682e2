<?php

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;

require __DIR__.'/../vendor/autoload.php';

$app = new Application(
    $_ENV['APP_ENV'] ?? 'production',
    $_ENV['APP_DEBUG'] ?? false
);

$app->bind('path.public', function() {
    return __DIR__.'/../public';
});

$app->make('Illuminate\Contracts\Http\Kernel')->handle(
    Illuminate\Http\Request::capture()
);

Route::middleware('web')->group(function () {
    Route::get('/import', [ImportController::class, 'showImportForm'])->name('import.form');
    Route::post('/import', [ImportController::class, 'import'])->name('import');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/export/{quarter}', [ExportController::class, 'export'])->name('export');
});

return $app;