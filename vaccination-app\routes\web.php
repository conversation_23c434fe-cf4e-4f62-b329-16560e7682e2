<?php

use App\Http\Controllers\ImportController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ExportController;
use Illuminate\Support\Facades\Route;

// Dashboard routes
Route::get('/', [DashboardController::class, 'index'])->name('home');
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Import routes with rate limiting
Route::middleware(['throttle:10,1'])->group(function () {
    Route::get('/import', [ImportController::class, 'showImportForm'])->name('import.form');
    Route::post('/import', [ImportController::class, 'import'])->name('import');
});

// Export routes with rate limiting
Route::middleware(['throttle:30,1'])->group(function () {
    Route::get('/export/{quarter}', [ExportController::class, 'export'])
        ->name('export')
        ->where('quarter', '[1-4]');
});