# tests/Feature/VaccinationTest.php
namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\Vaccination;

class VaccinationTest extends TestCase
{
    use RefreshDatabase;

    public function test_import_vaccination_data()
    {
        $response = $this->post('/import', [
            'file' => \Illuminate\Http\UploadedFile::fake()->create('vaccination_data.xlsx', 100)
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('vaccinations', [
            'vaccine_name' => 'Sample Vaccine',
            'quantity' => 100
        ]);
    }

    public function test_dashboard_displays_vaccination_data()
    {
        Vaccination::create([
            'month' => 'janvier',
            'year' => 2023,
            'vaccine_name' => 'Sample Vaccine',
            'quantity' => 100
        ]);

        $response = $this->get('/dashboard?year=2023');

        $response->assertStatus(200);
        $response->assertSee('Vaccinations pour 2023');
        $response->assertSee('Sample Vaccine');
        $response->assertSee(100);
    }

    public function test_export_vaccination_data()
    {
        Vaccination::create([
            'month' => 'janvier',
            'year' => 2023,
            'vaccine_name' => 'Sample Vaccine',
            'quantity' => 100
        ]);

        $response = $this->get('/export/1?year=2023');

        $response->assertStatus(200);
        $this->assertEquals('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', $response->headers->get('Content-Type'));
    }
}