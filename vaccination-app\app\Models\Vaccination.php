namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Vaccination extends Model
{
    use HasFactory;

    protected $fillable = ['month', 'year', 'vaccine_name', 'quantity'];

    protected $casts = [
        'year' => 'integer',
        'quantity' => 'integer',
    ];

    // Validation rules
    public static function rules()
    {
        return [
            'month' => 'required|string|max:20|in:janvier,février,mars,avril,mai,juin,juillet,août,septembre,octobre,novembre,décembre',
            'year' => 'required|integer|min:1900|max:2100',
            'vaccine_name' => 'required|string|max:100',
            'quantity' => 'required|integer|min:0',
        ];
    }

    // Scopes for common queries
    public function scopeByYear($query, $year)
    {
        return $query->where('year', $year);
    }

    public function scopeByMonth($query, $month)
    {
        return $query->where('month', $month);
    }

    public function scopeByVaccine($query, $vaccineName)
    {
        return $query->where('vaccine_name', $vaccineName);
    }

    public function scopeByQuarter($query, $quarter, $year)
    {
        $months = self::getMonthsForQuarter($quarter);
        return $query->where('year', $year)->whereIn('month', $months);
    }

    // Helper methods
    public static function getMonthsForQuarter($quarter)
    {
        switch ($quarter) {
            case 1:
                return ['janvier', 'février', 'mars'];
            case 2:
                return ['avril', 'mai', 'juin'];
            case 3:
                return ['juillet', 'août', 'septembre'];
            case 4:
                return ['octobre', 'novembre', 'décembre'];
            default:
                throw new \InvalidArgumentException('Trimestre invalide');
        }
    }

    public static function getAllMonths()
    {
        return [
            'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
            'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
        ];
    }
}